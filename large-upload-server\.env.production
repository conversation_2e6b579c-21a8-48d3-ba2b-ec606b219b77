# Production Environment Variables
NODE_ENV=production
PORT=3000

# Upload Configuration
MAX_FILE_SIZE=50GB
CHUNK_SIZE=10MB
MAX_CONCURRENT_UPLOADS=10

# Security
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
TRUST_PROXY=true

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Performance
CLUSTER_WORKERS=0  # 0 = auto detect CPU cores
KEEP_ALIVE_TIMEOUT=300000
REQUEST_TIMEOUT=300000

# Storage
UPLOAD_DIR=/var/uploads
TEMP_DIR=/tmp/uploads

# Database (if needed)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=upload_db
