import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  UploadSettings,
  ChunkProgress,
  StatusMessage,
  UploadResult,
  UploadTask,
  ServerStatus,
} from '@/types/upload'
import { URL_BASE_API_UPLOAD, CHUNK_SIZE } from '@/constants/upload'
import axios from 'axios'

// --- Optimized Configuration ---
const baseURL = URL_BASE_API_UPLOAD || 'http://localhost:3000'
const OPTIMAL_CONCURRENCY = 6 // 5-7 luồng song song
const MAX_RETRIES = 3
const RETRY_BASE_DELAY = 1000 // 1 second
const BATCH_DELAY = 1000 // 1 second delay between batches

export const useUploadStore = defineStore('upload', () => {
  // --- State ---
  const uploadTasks = ref<UploadTask[]>([])
  const statusMessages = ref<StatusMessage[]>([])
  const activeTaskIndex = ref<number | null>(null)
  const serverStatus = ref<ServerStatus>({ connected: false })
  const checking = ref(false)
  const selectedFile = ref<File | null>(null)
  const isDragOver = ref(false)
  const uploading = ref(false)
  const uploadPaused = ref(false)
  const progress = ref(0)
  const currentChunk = ref(0)

  // Upload statistics
  const startTime = ref(0)
  const uploadedBytes = ref(0)
  const uploadSpeed = ref(0)
  const lastProgressUpdate = ref(0)
  const lastUploadedBytes = ref(0)

  // --- Helper functions ---
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
  }

  function addStatusMessage(type: StatusMessage['type'], text: string) {
    statusMessages.value.unshift({
      timestamp: Date.now(),
      type,
      text,
    })
    if (statusMessages.value.length > 50) statusMessages.value = statusMessages.value.slice(0, 50)
  }

  // Hash function (SHA-256) for single buffer
  async function calcSha256(buf: ArrayBuffer): Promise<string> {
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', buf)
    return Array.prototype.map
      .call(new Uint8Array(hashBuffer), (x: number) => ('00' + x.toString(16)).slice(-2))
      .join('')
  }

  // Optimized hash calculation with progress tracking
  async function hashFile(file: File): Promise<string> {
    const HASH_CHUNK_SIZE = 64 * 1024 * 1024 // 64MB chunks for hashing
    let offset = 0
    const chunkHashes: string[] = []

    addStatusMessage('info', `Calculating hash for ${formatFileSize(file.size)} file...`)

    while (offset < file.size) {
      const chunkEnd = Math.min(offset + HASH_CHUNK_SIZE, file.size)
      const chunk = file.slice(offset, chunkEnd)

      const arrayBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
        reader.onerror = reject
        reader.readAsArrayBuffer(chunk)
      })

      const chunkHash = await calcSha256(arrayBuffer)
      chunkHashes.push(chunkHash)
      offset = chunkEnd

      // Progress update every 10%
      const progress = Math.round((offset / file.size) * 100)
      if (progress % 10 === 0) {
        addStatusMessage('info', `Hash calculation progress: ${progress}%`)
      }

      // Small delay to prevent UI blocking
      if (chunkHashes.length % 3 === 0) {
        await new Promise((resolve) => setTimeout(resolve, 1))
      }
    }

    // Create final combined hash
    const combinedHashString = chunkHashes.join('')
    const combinedBuffer = new TextEncoder().encode(combinedHashString)
    const finalHashBuffer = await window.crypto.subtle.digest('SHA-256', combinedBuffer)

    const finalHash = Array.prototype.map
      .call(new Uint8Array(finalHashBuffer), (x: number) => ('00' + x.toString(16)).slice(-2))
      .join('')

    addStatusMessage('success', 'File hash calculation completed')
    return finalHash
  }

  // --- Upload task functions ---
  function addUploadTask(file: File, settings: UploadSettings, typeFile: string) {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    
    // Optimize settings for better performance
    const optimizedSettings = {
      ...settings,
      parallelUpload: true, // Always use parallel upload
      maxConcurrent: Math.min(settings.maxConcurrent || OPTIMAL_CONCURRENCY, 7), // Cap at 7
    }

    uploadTasks.value.push({
      uploadId: '',
      typeFileData: typeFile,
      file,
      filename: file.name,
      filesize: file.size,
      status: 'waiting',
      progress: 0,
      currentChunk: 0,
      totalChunks,
      chunkProgress: Array(totalChunks)
        .fill(null)
        .map(() => ({
          status: 'pending' as const,
          progress: 0,
        })),
      uploadResult: null,
      paused: false,
      settings: optimizedSettings,
      startTime: 0,
      speed: 0,
    })

    addStatusMessage('info', `Added task: ${file.name} (${totalChunks} chunks, ${optimizedSettings.maxConcurrent} concurrent)`)
    if (activeTaskIndex.value === null) activeTaskIndex.value = 0
  }

  // Optimized chunk upload with retry mechanism and better error handling
  async function uploadChunk(task: UploadTask, chunkIndex: number): Promise<void> {
    const file = task.file
    const start = chunkIndex * CHUNK_SIZE
    const end = Math.min(file.size, start + CHUNK_SIZE)
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const blob = file.slice(start, end)
        const arrayBuffer = await blob.arrayBuffer()
        const chunkHash = await calcSha256(arrayBuffer)
        const formData = new FormData()
        
        formData.append('uploadId', task.uploadId)
        formData.append('chunkIndex', chunkIndex.toString())
        formData.append('chunkHash', chunkHash)
        formData.append('chunk', new Blob([arrayBuffer], { type: 'application/octet-stream' }))
        
        await axios.post(`${baseURL}/api/upload/upload-chunk`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 60000, // 1 minute timeout
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        })
        
        // Success - exit retry loop
        return
        
      } catch (error: any) {
        console.warn(`Chunk ${chunkIndex} attempt ${attempt}/${MAX_RETRIES} failed:`, error.message)
        
        if (attempt === MAX_RETRIES) {
          throw new Error(`Chunk ${chunkIndex} failed after ${MAX_RETRIES} attempts: ${error.message}`)
        }
        
        // Progressive backoff: 1s, 2s, 4s
        const waitTime = RETRY_BASE_DELAY * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
  }

  // --- Core upload logic ---
  async function startTask(index: number, autoExtract: boolean = false) {
    const task = uploadTasks.value[index]
    if (!task) return
    
    task.status = 'uploading'
    task.paused = false
    task.progress = 0
    task.currentChunk = 0
    task.startTime = Date.now()
    addStatusMessage('info', `Start uploading: ${task.filename}`)

    try {
      // Calculate file hash
      addStatusMessage('info', 'Calculating file hash...')
      const fileHash = await hashFile(task.file)
      
      // Initialize upload session
      addStatusMessage('info', 'Initializing upload session...')
      const res = await axios.post(`${baseURL}/api/upload/init-upload`, {
        filename: task.filename,
        filesize: task.filesize,
        filehash: fileHash,
        totalChunks: task.totalChunks,
      })
      task.uploadId = res.data.uploadId

      // Always use optimized parallel upload
      await uploadParallelOptimized(task)

      if (!task.paused) {
        // Complete upload
        addStatusMessage('info', 'Finalizing upload...')
        const result = await axios.post(`${baseURL}/api/upload/complete-upload`, {
          uploadId: task.uploadId,
        })
        
        task.uploadResult = {
          success: true,
          autoExtract: autoExtract,
          uploadId: task.uploadId,
          filename: task.filename,
          filesize: task.filesize,
          finalPath: result.data.finalPath,
          extractLog: result.data.extractLog,
        }
        task.status = 'completed'
        task.progress = 100
        addStatusMessage('success', `Upload completed: ${task.filename}`)
      }
    } catch (error: any) {
      task.status = 'error'
      task.error = error.message
      task.uploadResult = {
        success: false,
        autoExtract: false,
        uploadId: task.uploadId,
        filename: task.filename,
        error: error.message,
      }
      addStatusMessage('error', `Upload failed: ${task.filename} (${error.message})`)
    }
  }

  // Optimized parallel upload with batch processing and better concurrency control
  async function uploadParallelOptimized(task: UploadTask) {
    const maxConcurrent = task.settings.maxConcurrent || OPTIMAL_CONCURRENCY
    const batchSize = maxConcurrent
    let completedChunks = 0
    let failedChunks = 0

    addStatusMessage('info', `Starting optimized parallel upload with ${maxConcurrent} concurrent streams`)

    // Process chunks in batches to avoid overwhelming server
    for (let batchStart = 0; batchStart < task.totalChunks; batchStart += batchSize) {
      if (task.paused) {
        task.status = 'paused'
        addStatusMessage('warning', `Paused: ${task.filename}`)
        return
      }

      const batchEnd = Math.min(batchStart + batchSize, task.totalChunks)
      const batchPromises: Promise<void>[] = []

      // Create batch of concurrent uploads
      for (let i = batchStart; i < batchEnd; i++) {
        task.chunkProgress[i].status = 'uploading'

        const uploadPromise = uploadChunk(task, i)
          .then(() => {
            task.chunkProgress[i].status = 'completed'
            task.chunkProgress[i].progress = 100
            completedChunks++
            task.currentChunk = completedChunks
            task.progress = Math.round((completedChunks / task.totalChunks) * 100)

            // Calculate speed
            const elapsed = (Date.now() - task.startTime) / 1000
            const avgSpeed = (completedChunks * CHUNK_SIZE / 1024 / 1024) / elapsed
            task.speed = avgSpeed

            addStatusMessage('info',
              `Chunk ${i + 1}/${task.totalChunks} completed | Progress: ${task.progress}% | Speed: ${avgSpeed.toFixed(1)} MB/s`
            )
          })
          .catch((error) => {
            task.chunkProgress[i].status = 'error'
            failedChunks++
            addStatusMessage('error', `Chunk ${i + 1} failed: ${error.message}`)
            throw error
          })

        batchPromises.push(uploadPromise)
      }

      // Wait for current batch to complete
      try {
        await Promise.all(batchPromises)
        addStatusMessage('info', `Batch ${Math.floor(batchStart/batchSize) + 1} completed successfully`)

        // Small delay between batches to let server recover
        if (batchEnd < task.totalChunks) {
          await new Promise(resolve => setTimeout(resolve, BATCH_DELAY))
        }
      } catch (error) {
        addStatusMessage('error', `Batch failed, stopping upload`)
        throw error
      }
    }

    const finalSpeed = task.speed
    addStatusMessage('success',
      `Upload completed: ${completedChunks} chunks, ${failedChunks} failed, avg speed: ${finalSpeed.toFixed(1)} MB/s`
    )
  }

  // Legacy sequential upload (kept for compatibility)
  async function uploadSequential(task: UploadTask) {
    for (let i = 0; i < task.totalChunks; i++) {
      if (task.paused) {
        task.status = 'paused'
        addStatusMessage('warning', `Paused: ${task.filename}`)
        return
      }
      try {
        task.chunkProgress[i].status = 'uploading'
        await uploadChunk(task, i)
        task.currentChunk = i + 1
        task.progress = Math.round((task.currentChunk / task.totalChunks) * 100)
        task.chunkProgress[i].status = 'completed'
        task.chunkProgress[i].progress = 100
        addStatusMessage('info', `Uploaded chunk ${i + 1}/${task.totalChunks} (${task.filename})`)

        // Small delay to prevent overwhelming server
        await new Promise((resolve) => setTimeout(resolve, 50))
      } catch (error) {
        task.chunkProgress[i].status = 'error'
        addStatusMessage('error', `Failed chunk ${i + 1}: ${task.filename}`)
        throw error
      }
    }
  }

  // Legacy parallel upload (replaced by optimized version)
  async function uploadParallel(task: UploadTask) {
    // Redirect to optimized version
    return uploadParallelOptimized(task)
  }

  // --- Control functions ---
  function pauseTask(index: number) {
    const task = uploadTasks.value[index]
    if (task) {
      task.paused = true
      task.status = 'paused'
      addStatusMessage('warning', `Paused upload: ${task.filename}`)
    }
  }

  function resumeTask(index: number) {
    const task = uploadTasks.value[index]
    if (task && task.status === 'paused') {
      task.paused = false
      task.status = 'uploading'
      startTask(index)
      addStatusMessage('info', `Resumed upload: ${task.filename}`)
    }
  }

  function removeTask(index: number) {
    const task = uploadTasks.value[index]
    if (task) {
      uploadTasks.value.splice(index, 1)
      addStatusMessage('warning', `Removed upload: ${task.filename}`)
      if (uploadTasks.value.length === 0) activeTaskIndex.value = null
    }
  }

  // --- Server Status ---
  const checkServerStatus = async () => {
    checking.value = true
    try {
      const response = await axios.get(`${baseURL}/api/status`)
      serverStatus.value = response.data
      addStatusMessage('info', 'Connected to server')
      return response.data
    } catch (error: any) {
      serverStatus.value = { connected: false, message: error.message }
      addStatusMessage('error', 'Failed to connect to server')
      return null
    } finally {
      checking.value = false
    }
  }

  // --- File handling ---
  const clearFile = () => {
    selectedFile.value = null
    resetUploadState()
  }

  // --- Upload state management ---
  const resetUploadState = () => {
    uploading.value = false
    uploadPaused.value = false
    progress.value = 0
    currentChunk.value = 0
    uploadedBytes.value = 0
    uploadSpeed.value = 0
    lastProgressUpdate.value = 0
    lastUploadedBytes.value = 0
    startTime.value = 0
  }

  // Computed getters
  const getUploadSpeed = computed(() => {
    if (uploadSpeed.value === 0) return '0 B/s'
    return `${formatFileSize(uploadSpeed.value)}/s`
  })

  const getElapsedTime = computed(() => {
    return startTime.value ? Date.now() - startTime.value : 0
  })

  // Get current task performance stats
  const getCurrentTaskStats = computed(() => {
    const task = activeTaskIndex.value !== null ? uploadTasks.value[activeTaskIndex.value] : null
    if (!task) return null

    return {
      filename: task.filename,
      progress: task.progress,
      speed: task.speed,
      currentChunk: task.currentChunk,
      totalChunks: task.totalChunks,
      concurrency: task.settings.maxConcurrent,
      status: task.status
    }
  })

  // --- Expose API ---
  return {
    // State
    uploadTasks,
    activeTaskIndex,
    statusMessages,
    serverStatus,
    checking,
    selectedFile,
    isDragOver,
    uploading,
    uploadPaused,
    progress,
    currentChunk,

    // Upload stats
    uploadSpeed: getUploadSpeed,
    elapsedTime: getElapsedTime,
    currentTaskStats: getCurrentTaskStats,

    // Methods
    formatFileSize,
    addStatusMessage,
    addUploadTask,
    startTask,
    pauseTask,
    resumeTask,
    removeTask,
    checkServerStatus,
    clearFile,
    resetUploadState,
  }
})
