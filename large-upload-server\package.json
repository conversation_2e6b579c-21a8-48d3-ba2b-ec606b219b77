{"name": "large-upload-server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js --ignore test-*.js --ignore uploads/ --ignore node_modules/", "test": "node src/app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "body-parser": "^2.2.0", "compression": "^1.8.1", "cors": "^2.8.5", "crypto": "^1.0.1", "express": "^5.1.0", "form-data": "^4.0.4", "fs-extra": "^11.3.1", "multer": "^2.0.2", "proper-lockfile": "^4.1.2", "shelljs": "^0.10.0", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}