const express = require('express');
const compression = require('compression');
const uploadRoutes = require('./routes/uploadRoutes');
const extractRoutes = require('./routes/extract.route');
const errorHandler = require('./middlewares/errorHandler');
const logger = require('./middlewares/logger');

const app = express();
const cors = require('cors');

// Performance optimizations
app.use(compression({
    filter: (req, res) => {
        // Don't compress upload endpoints to avoid overhead
        if (req.url.includes('/upload-chunk')) {
            return false;
        }
        return compression.filter(req, res);
    },
    threshold: 1024 // Only compress responses larger than 1KB
}));

// CORS configuration
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204
}));

// Configure middleware - Remove body parser limits for large files
app.use(express.json({ limit: '50gb' }));
app.use(express.urlencoded({ limit: '50gb', extended: true }));
app.use(express.raw({ limit: '50gb' }));

// Disable X-Powered-By header for security
app.disable('x-powered-by');

// Trust proxy for better performance behind load balancers
app.set('trust proxy', 1);

app.use(logger);

// Routes
app.use('/api/upload', uploadRoutes);
app.use('/api/extract', extractRoutes);
app.get('/test', (req, res) => {
    res.send(`
        <h2>API Test thành công!</h2>
        <p>Nếu bạn thấy dòng này nghĩa là backend đang chạy OK và KHÔNG bị lỗi CORS với GET /test.</p>
        <p>Origin: ${req.headers.origin || 'None'}</p>
    `);
});

// Error handling
app.use(errorHandler);

const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
    console.log(`Server started on port ${PORT}`);
});

// Increase server timeout for large file uploads
server.timeout = 300000; // 5 minutes
server.keepAliveTimeout = 300000; // 5 minutes
server.headersTimeout = 300000; // 5 minutes

// Handle server errors
server.on('error', (error) => {
    console.error('Server error:', error);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
