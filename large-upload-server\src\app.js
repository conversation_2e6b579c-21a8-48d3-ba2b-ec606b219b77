const express = require('express');
const compression = require('compression');
const uploadRoutes = require('./routes/uploadRoutes');
const extractRoutes = require('./routes/extract.route');
const errorHandler = require('./middlewares/errorHandler');
const logger = require('./middlewares/logger');

const app = express();
const cors = require('cors');

// Performance optimizations
app.use(compression({
    filter: (req, res) => {
        // Don't compress upload endpoints to avoid overhead
        if (req.url.includes('/upload-chunk')) {
            return false;
        }
        return compression.filter(req, res);
    },
    threshold: 1024 // Only compress responses larger than 1KB
}));

// CORS configuration
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204
}));

// Configure middleware - Remove body parser limits for large files
app.use(express.json({ limit: '50gb' }));
app.use(express.urlencoded({ limit: '50gb', extended: true }));
app.use(express.raw({ limit: '50gb' }));

// Disable X-Powered-By header for security
app.disable('x-powered-by');

// Trust proxy for better performance behind load balancers
app.set('trust proxy', 1);

// Add request queue management for upload endpoints
const activeUploads = new Map();
const MAX_CONCURRENT_UPLOADS = 10;

app.use('/api/upload/upload-chunk', (req, res, next) => {
    const uploadId = req.body?.uploadId || 'unknown';
    const currentCount = activeUploads.get(uploadId) || 0;

    if (currentCount >= MAX_CONCURRENT_UPLOADS) {
        return res.status(429).json({
            error: 'Too many concurrent uploads for this session',
            retryAfter: 1000
        });
    }

    activeUploads.set(uploadId, currentCount + 1);

    // Clean up counter when request finishes
    const cleanup = () => {
        const count = activeUploads.get(uploadId) || 0;
        if (count <= 1) {
            activeUploads.delete(uploadId);
        } else {
            activeUploads.set(uploadId, count - 1);
        }
    };

    res.on('finish', cleanup);
    res.on('close', cleanup);
    res.on('error', cleanup);

    next();
});

app.use(logger);

// Routes
app.use('/api/upload', uploadRoutes);
app.use('/api/extract', extractRoutes);
app.get('/test', (req, res) => {
    res.send(`
        <h2>API Test thành công!</h2>
        <p>Nếu bạn thấy dòng này nghĩa là backend đang chạy OK và KHÔNG bị lỗi CORS với GET /test.</p>
        <p>Origin: ${req.headers.origin || 'None'}</p>
    `);
});

// Error handling
app.use(errorHandler);

const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
    console.log(`Server started on port ${PORT}`);
});

// Optimize server for high concurrency and large uploads
server.timeout = 300000; // 5 minutes
server.keepAliveTimeout = 300000; // 5 minutes
server.headersTimeout = 300000; // 5 minutes
server.requestTimeout = 300000; // 5 minutes
server.maxConnections = 1000; // Increase max connections
server.maxRequestsPerSocket = 0; // No limit on requests per socket

// Optimize for high throughput
server.on('connection', (socket) => {
    socket.setNoDelay(true); // Disable Nagle's algorithm
    socket.setKeepAlive(true, 300000); // Keep connections alive
});

// Handle server errors
server.on('error', (error) => {
    console.error('Server error:', error);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
