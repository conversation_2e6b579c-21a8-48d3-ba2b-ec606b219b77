const axios = require("axios");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Helper functions
function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

function calculateCombinedHash(buffer, chunkSize = 64 * 1024 * 1024) {
  const finalHash = crypto.createHash('sha256');
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    const chunkHash = crypto.createHash('sha256').update(chunk).digest('hex');
    finalHash.update(chunkHash);
    offset += chunkSize;
  }
  
  return finalHash.digest('hex');
}

// Test error cases
async function testErrorCases() {
  try {
    console.log("Testing error handling...");

    // Test 1: Invalid init-upload data
    console.log("\n1. Testing invalid init-upload data...");
    try {
      await axios.post(`${baseURL}/api/upload/init-upload`, {
        // Missing required fields
      });
      console.log("❌ Should have failed with missing fields");
    } catch (error) {
      if (error.response?.status === 400) {
        console.log("✓ Correctly rejected invalid init data:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 2: Invalid filesize
    console.log("\n2. Testing invalid filesize...");
    try {
      await axios.post(`${baseURL}/api/upload/init-upload`, {
        filename: "test.txt",
        filesize: -1, // Invalid size
        totalChunks: 1
      });
      console.log("❌ Should have failed with invalid filesize");
    } catch (error) {
      if (error.response?.status === 400) {
        console.log("✓ Correctly rejected invalid filesize:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 3: Upload chunk without uploadId
    console.log("\n3. Testing chunk upload without uploadId...");
    try {
      const formData = new FormData();
      formData.append("chunkIndex", "0");
      formData.append("chunkHash", "somehash");
      formData.append("chunk", Buffer.from("test"), {
        filename: "chunk_0",
        contentType: "application/octet-stream",
      });

      await axios.post(`${baseURL}/api/upload/upload-chunk`, formData, {
        headers: { ...formData.getHeaders() },
      });
      console.log("❌ Should have failed without uploadId");
    } catch (error) {
      if (error.response?.status === 400) {
        console.log("✓ Correctly rejected missing uploadId:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 4: Upload chunk with invalid uploadId
    console.log("\n4. Testing chunk upload with invalid uploadId...");
    try {
      const formData = new FormData();
      formData.append("uploadId", "invalid-upload-id");
      formData.append("chunkIndex", "0");
      formData.append("chunkHash", "somehash");
      formData.append("chunk", Buffer.from("test"), {
        filename: "chunk_0",
        contentType: "application/octet-stream",
      });

      await axios.post(`${baseURL}/api/upload/upload-chunk`, formData, {
        headers: { ...formData.getHeaders() },
      });
      console.log("❌ Should have failed with invalid uploadId");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected invalid uploadId:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 5: Get status with invalid uploadId
    console.log("\n5. Testing status with invalid uploadId...");
    try {
      await axios.get(`${baseURL}/api/upload/upload-status`, {
        params: { uploadId: "invalid-upload-id" },
      });
      console.log("❌ Should have failed with invalid uploadId");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected invalid uploadId:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 6: Complete upload with missing chunks
    console.log("\n6. Testing complete upload with missing chunks...");
    
    // First create a valid upload
    const testBuffer = Buffer.from("test content");
    const fileHash = calculateCombinedHash(testBuffer);
    
    const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, {
      filename: "incomplete-test.txt",
      filesize: testBuffer.length,
      filehash: fileHash,
      totalChunks: 2, // Say we have 2 chunks but only upload 1
    });
    
    const uploadId = initResponse.data.uploadId;
    console.log("✓ Created upload session:", uploadId);
    
    // Upload only first chunk
    const formData = new FormData();
    formData.append("uploadId", uploadId);
    formData.append("chunkIndex", "0");
    formData.append("chunkHash", calculateHash(testBuffer));
    formData.append("chunk", testBuffer, {
      filename: "chunk_0",
      contentType: "application/octet-stream",
    });

    await axios.post(`${baseURL}/api/upload/upload-chunk`, formData, {
      headers: { ...formData.getHeaders() },
    });
    console.log("✓ Uploaded chunk 0");
    
    // Try to complete with missing chunk 1
    try {
      await axios.post(`${baseURL}/api/upload/complete-upload`, {
        uploadId,
      });
      console.log("❌ Should have failed with missing chunks");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected incomplete upload:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    // Test 7: Hash mismatch
    console.log("\n7. Testing hash mismatch...");
    
    const testBuffer2 = Buffer.from("another test content");
    const correctHash = calculateCombinedHash(testBuffer2);
    const wrongHash = "wrong_hash_value";
    
    const initResponse2 = await axios.post(`${baseURL}/api/upload/init-upload`, {
      filename: "hash-mismatch-test.txt",
      filesize: testBuffer2.length,
      filehash: wrongHash, // Wrong hash
      totalChunks: 1,
    });
    
    const uploadId2 = initResponse2.data.uploadId;
    
    // Upload chunk
    const formData2 = new FormData();
    formData2.append("uploadId", uploadId2);
    formData2.append("chunkIndex", "0");
    formData2.append("chunkHash", calculateHash(testBuffer2));
    formData2.append("chunk", testBuffer2, {
      filename: "chunk_0",
      contentType: "application/octet-stream",
    });

    await axios.post(`${baseURL}/api/upload/upload-chunk`, formData2, {
      headers: { ...formData2.getHeaders() },
    });
    
    // Try to complete - should fail due to hash mismatch
    try {
      await axios.post(`${baseURL}/api/upload/complete-upload`, {
        uploadId: uploadId2,
      });
      console.log("❌ Should have failed with hash mismatch");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly detected hash mismatch:", error.response.data.error);
      } else {
        console.log("❌ Unexpected error:", error.response?.data);
      }
    }

    console.log("\n✅ Error handling tests completed!");
  } catch (error) {
    console.error("❌ Error handling test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
  }
}

// Run tests
testErrorCases();
